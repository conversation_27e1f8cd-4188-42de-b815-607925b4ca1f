import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_KEY in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Calculate recommended ship-in quantity based on sales rank
 * @param {number} rank - Sales rank from Keepa data
 * @returns {number|null} - Recommended quantity or null if rank is too high
 */
function calculateRecommendedQuantity(rank) {
  if (!rank || rank > 2129) return null;
  
  if (rank < 300) return 5;
  if (rank < 600) return 4;
  if (rank < 1000) return 3;
  if (rank < 1500) return 2;
  if (rank < 2130) return 1;
  
  return null;
}

/**
 * Check if the FBA inventory report table exists and is accessible
 */
async function checkFbaInventoryReportTable() {
  console.log('Checking FBA inventory report table access...');

  try {
    // Test table access by getting count
    const { count, error } = await supabase
      .from('import_table_amaz_fba_inv_rpt')
      .select('*', { count: 'exact', head: true });

    if (error) {
      throw new Error(`Cannot access import_table_amaz_fba_inv_rpt table: ${error.message}`);
    }

    console.log(`FBA inventory report table accessible with ${count || 0} existing records`);
    return true;
  } catch (err) {
    throw new Error(`FBA inventory report table check failed: ${err.message}`);
  }
}

/**
 * Find t_sdasins records that need FBA ship-in recommendations
 */
async function findCandidateRecords() {
  console.log('Finding candidate t_sdasins records...');

  // First, get count of all FBA-enabled sdasins with sales rank data
  const { count: totalCount, error: countError } = await supabase
    .from('t_sdasins')
    .select('*', { count: 'exact', head: true })
    .eq('fba', 'Y')
    .not('fbafnsku', 'is', null)
    .not('fba_sku', 'is', null)
    .not('so_rank_30day_avg', 'is', null);

  if (countError) {
    throw new Error(`Error counting t_sdasins records: ${countError.message}`);
  }

  console.log(`Found ${totalCount} total FBA-enabled sdasins with sales rank data`);

  // Process records in chunks to avoid Supabase limits
  const chunkSize = 1000;
  const candidateRecords = [];
  let processedCount = 0;

  for (let offset = 0; offset < totalCount; offset += chunkSize) {
    const chunkNumber = Math.floor(offset / chunkSize) + 1;
    const totalChunks = Math.ceil(totalCount / chunkSize);

    console.log(`Processing chunk ${chunkNumber}/${totalChunks} (records ${offset + 1}-${Math.min(offset + chunkSize, totalCount)})`);

    // Get chunk of sdasin records
    const { data: sdasinRecords, error: sdasinError } = await supabase
      .from('t_sdasins')
      .select('id, fba_sku, fbafnsku, asin, so_rank_30day_avg')
      .eq('fba', 'Y')
      .not('fbafnsku', 'is', null)
      .not('fba_sku', 'is', null)
      .not('so_rank_30day_avg', 'is', null)
      .range(offset, offset + chunkSize - 1);

    if (sdasinError) {
      throw new Error(`Error fetching t_sdasins chunk: ${sdasinError.message}`);
    }

    // Process each record in the chunk
    for (const sdasin of sdasinRecords) {
      processedCount++;

      const recommendedQty = calculateRecommendedQuantity(sdasin.so_rank_30day_avg);

      // Skip if rank is too high
      if (recommendedQty === null) {
        continue;
      }

      // Check if record exists in FBA inventory report table
      // Note: The table uses fnsku as primary key, but we'll check by sku first, then fnsku
      const { data: existingRecords, error: checkError } = await supabase
        .from('import_table_amaz_fba_inv_rpt')
        .select('fnsku, available, "Recommended ship-in quantity"')
        .or(`sku.eq.${sdasin.fba_sku},fnsku.eq.${sdasin.fbafnsku}`);

      if (checkError) {
        console.warn(`Error checking existing record for SKU ${sdasin.fba_sku}: ${checkError.message}`);
        continue;
      }

      const existingRecord = existingRecords && existingRecords.length > 0 ? existingRecords[0] : null;

      // Determine if we need to create/update a record
      let needsRecord = false;

      if (!existingRecord) {
        // No record exists - need to create one
        needsRecord = true;
      } else if (
        (existingRecord.available === null || existingRecord.available === 0) &&
        existingRecord['Recommended ship-in quantity'] === null
      ) {
        // Record exists but meets criteria for update
        needsRecord = true;
      }

      if (needsRecord) {
        candidateRecords.push({
          sdasin_id: sdasin.id,
          sku: sdasin.fba_sku,
          fnsku: sdasin.fbafnsku,
          asin: sdasin.asin,
          sales_rank: sdasin.so_rank_30day_avg,
          recommended_quantity: recommendedQty,
          existing_record: existingRecord
        });
      }
    }

    // Small delay between chunks to be gentle on the database
    if (offset + chunkSize < totalCount) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  console.log(`Processed ${processedCount} total records`);
  console.log(`Found ${candidateRecords.length} records that need FBA ship-in recommendations`);
  return candidateRecords;
}

/**
 * Create or update FBA inventory report records
 */
async function createFbaRecommendations(candidateRecords) {
  console.log('Creating/updating FBA ship-in recommendations...');
  
  const currentTimestamp = new Date().toISOString();
  let createdCount = 0;
  let updatedCount = 0;
  let errorCount = 0;
  
  for (const record of candidateRecords) {
    try {
      const recordData = {
        sku: record.sku,
        fnsku: record.fnsku,
        asin: record.asin,
        'Recommended ship-in quantity': record.recommended_quantity,
        'snapshot-date': currentTimestamp.split('T')[0] // Convert to date format
      };

      if (record.existing_record) {
        // Update existing record using fnsku as primary key
        const { error } = await supabase
          .from('import_table_amaz_fba_inv_rpt')
          .update(recordData)
          .eq('fnsku', record.fnsku);

        if (error) {
          console.error(`Error updating record for SKU ${record.sku}: ${error.message}`);
          errorCount++;
        } else {
          updatedCount++;
          console.log(`Updated SKU ${record.sku}: rank ${record.sales_rank} → ${record.recommended_quantity} units`);
        }
      } else {
        // Create new record
        recordData.available = 0; // Default available to 0 for new records

        const { error } = await supabase
          .from('import_table_amaz_fba_inv_rpt')
          .insert(recordData);

        if (error) {
          console.error(`Error creating record for SKU ${record.sku}: ${error.message}`);
          errorCount++;
        } else {
          createdCount++;
          console.log(`Created SKU ${record.sku}: rank ${record.sales_rank} → ${record.recommended_quantity} units`);
        }
      }
    } catch (err) {
      console.error(`Exception processing SKU ${record.sku}: ${err.message}`);
      errorCount++;
    }
  }
  
  return { createdCount, updatedCount, errorCount };
}

/**
 * Main function to generate FBA ship-in recommendations
 */
async function main() {
  console.log('=== FBA Ship-In Quantity Recommendations Generator ===');
  console.log(`Started at: ${new Date().toISOString()}\n`);
  
  try {
    // Step 1: Check the FBA inventory report table access
    await checkFbaInventoryReportTable();
    
    // Step 2: Find candidate records
    const candidateRecords = await findCandidateRecords();
    
    if (candidateRecords.length === 0) {
      console.log('No records found that need FBA ship-in recommendations.');
      return;
    }
    
    // Step 3: Create/update recommendations
    const results = await createFbaRecommendations(candidateRecords);
    
    console.log('\n=== Summary ===');
    console.log(`Records processed: ${candidateRecords.length}`);
    console.log(`New records created: ${results.createdCount}`);
    console.log(`Existing records updated: ${results.updatedCount}`);
    console.log(`Errors encountered: ${results.errorCount}`);
    console.log(`Completed at: ${new Date().toISOString()}`);
    
  } catch (error) {
    console.error('Fatal error in main process:', error);
    process.exit(1);
  }
}

// Run the script
main();
