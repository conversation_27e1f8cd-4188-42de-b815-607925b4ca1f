import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_KEY in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Keepa API configuration
const KEEPA_API_KEY = 'urmlcoc8brbp56celtr1i100gqmo43to7a0q9ospldotdfl5k8idmf6tbt8g7al1';
const KEEPA_API_BASE_URL = 'https://api.keepa.com';
const CATEGORY_ID = '3406051'; // Remove the domain prefix, just use the category number
const DOMAIN_ID = 1; // US domain

/**
 * Fetch Best Sellers data from Keepa API
 * @returns {Promise<string[]>} Array of ASINs ordered by sales rank
 */
async function fetchKeepabestSellers() {
  const url = `${KEEPA_API_BASE_URL}/bestsellers/?key=${KEEPA_API_KEY}&domain=${DOMAIN_ID}&category=${CATEGORY_ID}`;
  
  console.log('Fetching Best Sellers data from Keepa API...');
  console.log('URL:', url);
  
  try {
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
    }
    
    const data = await response.json();
    
    // Log the response structure for debugging
    console.log('Keepa API response structure:', Object.keys(data));
    console.log('Full response:', JSON.stringify(data, null, 2));

    // The response should contain a bestSellersList object with asinList
    if (data.bestSellersList && data.bestSellersList.asinList && Array.isArray(data.bestSellersList.asinList)) {
      console.log(`Received ${data.bestSellersList.asinList.length} ASINs from Keepa API`);
      console.log(`Category ID in response: ${data.bestSellersList.categoryId}`);
      console.log(`Domain ID in response: ${data.bestSellersList.domainId}`);
      return data.bestSellersList.asinList;
    } else if (data.asinList && Array.isArray(data.asinList)) {
      console.log(`Received ${data.asinList.length} ASINs from Keepa API (direct asinList)`);
      return data.asinList;
    } else if (Array.isArray(data)) {
      console.log(`Received ${data.length} ASINs from Keepa API (direct array)`);
      return data;
    } else {
      console.error('Unexpected response format from Keepa API. Expected bestSellersList.asinList');
      console.error('Available fields:', Object.keys(data));
      if (data.bestSellersList) {
        console.error('bestSellersList fields:', Object.keys(data.bestSellersList));
      }
      throw new Error('Unexpected response format from Keepa API');
    }
  } catch (error) {
    console.error('Error fetching data from Keepa API:', error);
    throw error;
  }
}

/**
 * Update sales ranks in the database
 * @param {string[]} asinList - Array of ASINs ordered by sales rank
 */
async function updateSalesRanks(asinList) {
  console.log('Starting database updates...');
  
  const currentTimestamp = new Date().toISOString();
  let updatedCount = 0;
  let notFoundCount = 0;
  
  // Process ASINs in batches to avoid overwhelming the database
  const batchSize = 50;
  
  for (let i = 0; i < asinList.length; i += batchSize) {
    const batch = asinList.slice(i, i + batchSize);
    console.log(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(asinList.length / batchSize)} (ASINs ${i + 1}-${Math.min(i + batchSize, asinList.length)})`);
    
    // Create updates for this batch
    const updates = batch.map((asin, batchIndex) => {
      const rank = i + batchIndex + 1; // Position in the overall list (1-based)
      return {
        asin: asin,
        rank: rank,
        timestamp: currentTimestamp
      };
    });
    
    // Update each ASIN in the batch
    for (const update of updates) {
      try {
        const { data, error } = await supabase
          .from('t_sdasins')
          .update({
            so_rank_30day_avg: update.rank,
            so_rank_30day_avg_date: update.timestamp
          })
          .eq('asin', update.asin)
          .select('id, asin');
        
        if (error) {
          console.error(`Error updating ASIN ${update.asin}:`, error);
          continue;
        }
        
        if (data && data.length > 0) {
          updatedCount++;
          console.log(`Updated ASIN ${update.asin} with rank ${update.rank}`);
        } else {
          notFoundCount++;
          console.log(`ASIN ${update.asin} not found in t_sdasins table`);
        }
      } catch (err) {
        console.error(`Exception updating ASIN ${update.asin}:`, err);
      }
    }
    
    // Small delay between batches to be gentle on the database
    if (i + batchSize < asinList.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  console.log('\nUpdate Summary:');
  console.log(`Total ASINs processed: ${asinList.length}`);
  console.log(`Successfully updated: ${updatedCount}`);
  console.log(`Not found in database: ${notFoundCount}`);
  console.log(`Update timestamp: ${currentTimestamp}`);
}

/**
 * Main function to orchestrate the Keepa rank update process
 */
async function main() {
  console.log('=== Keepa Best Sellers Rank Update ===');
  console.log(`Category ID: ${CATEGORY_ID}`);
  console.log(`Domain ID: ${DOMAIN_ID} (US)`);
  console.log(`Started at: ${new Date().toISOString()}\n`);
  
  try {
    // Step 1: Fetch Best Sellers data from Keepa API
    const asinList = await fetchKeepabestSellers();
    
    if (!asinList || asinList.length === 0) {
      console.log('No ASINs received from Keepa API. Exiting.');
      return;
    }
    
    // Step 2: Update sales ranks in the database
    await updateSalesRanks(asinList);
    
    console.log('\n=== Update Complete ===');
    
  } catch (error) {
    console.error('Fatal error in main process:', error);
    process.exit(1);
  }
}

// Run the script
main();
